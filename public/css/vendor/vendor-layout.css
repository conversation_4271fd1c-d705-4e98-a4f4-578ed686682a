/* ===== MODERN VENDOR LAYOUT CSS ===== */
/* Based on home.blade.php design with light green theme */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* === CSS VARIABLES === */
:root {
    /* Primary Colors - Light Green Theme */
    --vendor-primary: #7ED957;
    --vendor-primary-dark: #5DC264;
    --vendor-primary-light: #E8FFE1;
    --vendor-teal: #4CAF50;
    --vendor-teal-dark: #3EA045;
    
    /* Background Colors */
    --vendor-bg: #f7f8fa;
    --vendor-bg-light: #F8FFF5;
    --vendor-surface: #ffffff;
    --vendor-surface-hover: #f9fafb;
    
    /* Text Colors */
    --vendor-text: #333333;
    --vendor-text-light: #666666;
    --vendor-text-muted: #9ca3af;
    
    /* Border & Shadow */
    --vendor-border: #e5e7eb;
    --vendor-border-light: #f3f4f6;
    --vendor-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --vendor-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    
    /* Layout Dimensions */
    --vendor-sidebar-width: 280px;
    --vendor-sidebar-collapsed: 80px;
    --vendor-header-height: 80px;
    
    /* Typography */
    --vendor-font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --vendor-font-size-xs: 0.75rem;
    --vendor-font-size-sm: 0.875rem;
    --vendor-font-size-base: 1rem;
    --vendor-font-size-lg: 1.125rem;
    --vendor-font-size-xl: 1.25rem;
    --vendor-font-size-2xl: 1.5rem;
    
    /* Spacing */
    --vendor-space-1: 0.25rem;
    --vendor-space-2: 0.5rem;
    --vendor-space-3: 0.75rem;
    --vendor-space-4: 1rem;
    --vendor-space-5: 1.25rem;
    --vendor-space-6: 1.5rem;
    --vendor-space-8: 2rem;
    --vendor-space-10: 2.5rem;
    --vendor-space-12: 3rem;
    
    /* Z-index */
    --vendor-z-dropdown: 1000;
    --vendor-z-sticky: 1020;
    --vendor-z-fixed: 1030;
    --vendor-z-modal: 1050;
}

/* === GLOBAL STYLES === */
.vendor-body, 
.modern-vendor-body {
    font-family: var(--vendor-font-family);
    background: var(--vendor-bg);
    color: var(--vendor-text);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: var(--vendor-font-size-base);
}

/* === MAIN LAYOUT STRUCTURE === */
.vendor-app,
.modern-vendor-app {
    display: flex;
    min-height: 100vh;
    background: var(--vendor-bg);
    position: relative;
}

/* === SIDEBAR STYLES === */
.vendor-sidebar {
    width: var(--vendor-sidebar-width);
    background: var(--vendor-surface);
    border-right: 1px solid var(--vendor-border);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--vendor-z-fixed);
    box-shadow: var(--vendor-shadow);
    overflow: hidden; /* Remove scrollbar */
    display: flex;
    flex-direction: column;
}

/* Remove collapsed state - sidebar always maximized */
.vendor-sidebar.collapsed {
    width: var(--vendor-sidebar-width); /* Keep full width always */
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-6) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border-light);
    background: var(--vendor-surface);
    min-height: var(--vendor-header-height);
    position: sticky;
    top: 0;
    z-index: var(--vendor-z-sticky);
}

/* Brand Logo */
.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    text-decoration: none;
    color: var(--vendor-text);
    transition: all 0.3s ease;
}

.brand-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-teal));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    flex-shrink: 0;
}

.brand-text {
    display: flex;
    flex-direction: column;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.brand-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text);
    line-height: 1.2;
}

.brand-subtitle {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    line-height: 1;
}

/* Sidebar Toggle - Hidden since sidebar is always maximized */
.sidebar-toggle {
    display: none;
}

/* Remove collapsed state styles - sidebar always shows full content */
.vendor-sidebar .brand-text,
.vendor-sidebar .nav-item-text,
.vendor-sidebar .nav-section-title {
    opacity: 1;
    visibility: visible;
}

/* === NAVIGATION STYLES === */
.sidebar-nav {
    padding: var(--vendor-space-4) 0;
    flex: 1;
    overflow-y: auto; /* Allow scrolling only in nav area if needed */
    overflow-x: hidden;
}

/* Hide scrollbar but keep functionality */
.sidebar-nav::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

.nav-section {
    margin-bottom: var(--vendor-space-6);
}

.nav-section-title {
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 var(--vendor-space-5);
    margin-bottom: var(--vendor-space-3);
    transition: opacity 0.3s ease;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: var(--vendor-space-1);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-5);
    color: var(--vendor-text-light);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    border-radius: 0 25px 25px 0;
    margin-right: var(--vendor-space-4);
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    background: var(--vendor-primary-light);
    color: var(--vendor-primary-dark);
    transform: translateX(4px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-teal));
    color: white;
    box-shadow: var(--vendor-shadow-lg);
}

.nav-icon {
    font-size: var(--vendor-font-size-lg);
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-item-text {
    transition: opacity 0.3s ease;
}

/* Badge */
.nav-badge {
    background: var(--vendor-primary);
    color: white;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

/* === MAIN CONTENT WRAPPER === */
.vendor-main-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: var(--vendor-sidebar-width); /* Always full sidebar width */
    min-height: 100vh;
    background: var(--vendor-bg);
}

/* Remove collapsed state - main wrapper always uses full sidebar width */

/* === HEADER STYLES === */
.vendor-header {
    background: var(--vendor-surface);
    border-bottom: 1px solid var(--vendor-border);
    height: var(--vendor-header-height);
    display: flex;
    align-items: center;
    padding: 0 var(--vendor-space-6);
    position: sticky;
    top: 0;
    z-index: var(--vendor-z-sticky);
    box-shadow: var(--vendor-shadow);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--vendor-space-4);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-xl);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: 8px;
    transition: all 0.2s ease;
    display: none;
}

.mobile-menu-toggle:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-primary);
}

/* Page Title */
.header-title {
    flex: 1;
}

.page-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0;
    line-height: 1.2;
}

.page-subtitle {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0;
    line-height: 1.2;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
}

.header-action-btn {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-lg);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
}

.header-action-btn:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-primary);
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: 12px;
    transition: all 0.2s ease;
}

.user-profile:hover {
    background: var(--vendor-surface-hover);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-teal));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--vendor-font-size-sm);
}

.user-info {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.user-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text);
    line-height: 1.2;
}

.user-role {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    line-height: 1.2;
}

/* === MAIN CONTENT AREA === */
.vendor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - var(--vendor-header-height));
}

.page-content {
    flex: 1;
    padding: var(--vendor-space-8);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* === MOBILE RESPONSIVE === */
@media (max-width: 1024px) {
    .vendor-sidebar {
        transform: translateX(-100%);
        z-index: var(--vendor-z-modal);
    }

    .vendor-sidebar.mobile-open {
        transform: translateX(0);
    }

    .vendor-main-wrapper {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .sidebar-toggle {
        display: none;
    }
}

@media (max-width: 768px) {
    .vendor-header {
        height: 64px;
        padding: 0 var(--vendor-space-4);
    }

    .page-content {
        padding: var(--vendor-space-6) var(--vendor-space-4);
    }

    .user-info {
        display: none;
    }

    .page-title {
        font-size: var(--vendor-font-size-lg);
    }

    .page-subtitle {
        display: none;
    }
}

/* === DROPDOWN STYLES === */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: 12px;
    box-shadow: var(--vendor-shadow-lg);
    min-width: 200px;
    z-index: var(--vendor-z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.dropdown.open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: var(--vendor-space-4);
    border-bottom: 1px solid var(--vendor-border-light);
}

.dropdown-header h3 {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    color: var(--vendor-text-light);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text);
}

.dropdown-divider {
    height: 1px;
    background: var(--vendor-border-light);
    margin: var(--vendor-space-2) 0;
}

/* === MOBILE OVERLAY === */
.vendor-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--vendor-z-modal) - 1);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.vendor-mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* === UTILITY CLASSES === */
.text-primary { color: var(--vendor-primary); }
.text-muted { color: var(--vendor-text-muted); }
.text-light { color: var(--vendor-text-light); }

.bg-primary { background: var(--vendor-primary); }
.bg-surface { background: var(--vendor-surface); }
.bg-light { background: var(--vendor-bg-light); }

.border-primary { border-color: var(--vendor-primary); }
.border-light { border-color: var(--vendor-border-light); }

.shadow { box-shadow: var(--vendor-shadow); }
.shadow-lg { box-shadow: var(--vendor-shadow-lg); }

.rounded { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }
.rounded-full { border-radius: 50%; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: var(--vendor-space-1); }
.gap-2 { gap: var(--vendor-space-2); }
.gap-3 { gap: var(--vendor-space-3); }
.gap-4 { gap: var(--vendor-space-4); }

.p-1 { padding: var(--vendor-space-1); }
.p-2 { padding: var(--vendor-space-2); }
.p-3 { padding: var(--vendor-space-3); }
.p-4 { padding: var(--vendor-space-4); }
.p-6 { padding: var(--vendor-space-6); }

.m-0 { margin: 0; }
.mb-2 { margin-bottom: var(--vendor-space-2); }
.mb-4 { margin-bottom: var(--vendor-space-4); }
.mb-6 { margin-bottom: var(--vendor-space-6); }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-xs { font-size: var(--vendor-font-size-xs); }
.text-sm { font-size: var(--vendor-font-size-sm); }
.text-base { font-size: var(--vendor-font-size-base); }
.text-lg { font-size: var(--vendor-font-size-lg); }
.text-xl { font-size: var(--vendor-font-size-xl); }

.hidden { display: none; }
.block { display: block; }

/* === ANIMATIONS === */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-fade-in { animation: fadeIn 0.3s ease-in-out; }
.animate-slide-in-right { animation: slideInRight 0.3s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
