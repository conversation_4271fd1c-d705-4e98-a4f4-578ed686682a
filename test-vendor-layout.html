<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendor Dashboard - WhaMart</title>
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Vendor Layout CSS -->
    <link rel="stylesheet" href="public/css/vendor/vendor-layout.css">
    
    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="vendor-body"
      x-data="vendorApp()"
      x-init="initApp()"
      :class="{
          'mobile-sidebar-open': mobileSidebarOpen
      }"
      @resize.window="handleResize()"
      @keydown.escape="closeMobileSidebar()"
      @click.away="closeDropdowns()">

    <!-- Mobile Sidebar Overlay -->
    <div x-show="mobileSidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeMobileSidebar()"
         class="vendor-mobile-overlay"
         :class="{ 'active': mobileSidebarOpen }"></div>

    <!-- Main App Container -->
    <div class="vendor-app">

        <!-- Vendor Sidebar -->
        <aside class="vendor-sidebar"
               :class="{ 'mobile-open': mobileSidebarOpen }">

            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="#" class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">WhaMart</div>
                        <div class="brand-subtitle">Vendor Panel</div>
                    </div>
                </a>

                <!-- Sidebar Toggle -->
                <button @click="toggleSidebar()" class="sidebar-toggle">
                    <i class="fas fa-chevron-left" :class="{ 'rotate-180': sidebarCollapsed }"></i>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link active">
                                <i class="nav-icon fas fa-home"></i>
                                <span class="nav-item-text">Dashboard</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-chart-line"></i>
                                <span class="nav-item-text">Analytics</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-shopping-bag"></i>
                                <span class="nav-item-text">Orders</span>
                                <span class="nav-badge">12</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-box"></i>
                                <span class="nav-item-text">Products</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-users"></i>
                                <span class="nav-item-text">Customers</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-cog"></i>
                                <span class="nav-item-text">Settings</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-life-ring"></i>
                                <span class="nav-item-text">Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- Main Content Wrapper -->
        <div class="vendor-main-wrapper">
            <!-- Header -->
            <header class="vendor-header">
                <div class="header-content">
                    <div class="header-left">
                        <!-- Mobile Menu Toggle -->
                        <button @click="toggleMobileSidebar()" class="mobile-menu-toggle">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Page Title -->
                        <div class="header-title">
                            <h1 class="page-title">Dashboard</h1>
                            <p class="page-subtitle">Manage your store and track performance</p>
                        </div>
                    </div>

                    <div class="header-right">
                        <!-- Notifications -->
                        <div class="dropdown" x-data="{ open: false }">
                            <button @click="open = !open" class="header-action-btn">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                            <div x-show="open" @click.away="open = false" class="dropdown-menu">
                                <div class="dropdown-header">
                                    <h3>Notifications</h3>
                                </div>
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-shopping-bag text-primary"></i>
                                    <span>New order received</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-star text-primary"></i>
                                    <span>New review posted</span>
                                </a>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="dropdown" x-data="{ open: false }">
                            <button @click="open = !open" class="user-profile">
                                <div class="user-avatar">V</div>
                                <div class="user-info">
                                    <div class="user-name">Vendor User</div>
                                    <div class="user-role">Vendor</div>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" class="dropdown-menu">
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-user"></i>
                                    <span>Profile</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <button class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>Logout</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="vendor-main">
                <div class="page-content">
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--vendor-shadow);">
                        <h2 style="color: var(--vendor-primary); margin-bottom: 1rem;">Welcome to Your Dashboard!</h2>
                        <p style="color: var(--vendor-text-light); margin-bottom: 2rem;">
                            This is your modern vendor dashboard with a clean, light green theme inspired by the home page design.
                        </p>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                            <div style="background: var(--vendor-bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid var(--vendor-primary);">
                                <h3 style="color: var(--vendor-text); margin-bottom: 0.5rem;">Total Sales</h3>
                                <p style="font-size: 2rem; font-weight: 600; color: var(--vendor-primary); margin: 0;">₹45,280</p>
                            </div>
                            
                            <div style="background: var(--vendor-bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid var(--vendor-teal);">
                                <h3 style="color: var(--vendor-text); margin-bottom: 0.5rem;">Orders</h3>
                                <p style="font-size: 2rem; font-weight: 600; color: var(--vendor-teal); margin: 0;">127</p>
                            </div>
                            
                            <div style="background: var(--vendor-bg-light); padding: 1.5rem; border-radius: 8px; border-left: 4px solid var(--vendor-primary-dark);">
                                <h3 style="color: var(--vendor-text); margin-bottom: 0.5rem;">Products</h3>
                                <p style="font-size: 2rem; font-weight: 600; color: var(--vendor-primary-dark); margin: 0;">89</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Vendor App JavaScript -->
    <script>
        function vendorApp() {
            return {
                sidebarCollapsed: false,
                mobileSidebarOpen: false,
                
                initApp() {
                    this.handleResize();
                },
                
                toggleSidebar() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                },
                
                toggleMobileSidebar() {
                    this.mobileSidebarOpen = !this.mobileSidebarOpen;
                },
                
                closeMobileSidebar() {
                    this.mobileSidebarOpen = false;
                },
                
                closeDropdowns() {
                    // Close any open dropdowns
                },
                
                handleResize() {
                    if (window.innerWidth >= 1024) {
                        this.mobileSidebarOpen = false;
                    }
                }
            }
        }
    </script>

</body>
</html>
